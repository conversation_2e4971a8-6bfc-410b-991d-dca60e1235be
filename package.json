{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:setup": "curl -X POST http://localhost:3000/api/db-test"}, "dependencies": {"@clerk/nextjs": "^6.23.0", "@radix-ui/react-slot": "^1.1.0", "@supabase/supabase-js": "^2.50.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.469.0", "next": "^15.3.4", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@types/node": "^22.9.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "15.1.3", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3"}}