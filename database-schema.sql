-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Organizations table (maps to Clerk organizations)
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  clerk_org_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  type TEXT NOT NULL DEFAULT 'hybrid' CHECK (type IN ('recruitment', 'bench_sales', 'hybrid')),
  settings JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User process roles (defines which processes users can access)
CREATE TABLE user_process_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL, -- Clerk user ID
  org_id UUID REFERENCES organizations(id) NOT NULL,
  process_type TEXT NOT NULL CHECK (process_type IN ('recruitment', 'bench_sales')),
  role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'recruiter', 'coordinator')),
  permissions JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security (RLS)
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_process_roles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for organizations
CREATE POLICY "Users can view their organization" ON organizations
  FOR SELECT USING (
    clerk_org_id IN (
      SELECT DISTINCT jsonb_array_elements_text(
        (auth.jwt() -> 'org_memberships')::jsonb
      )
    )
  );

CREATE POLICY "Users can update their organization" ON organizations
  FOR UPDATE USING (
    clerk_org_id IN (
      SELECT DISTINCT jsonb_array_elements_text(
        (auth.jwt() -> 'org_memberships')::jsonb
      )
    )
  );

-- RLS Policies for user_process_roles
CREATE POLICY "Users can view roles in their organization" ON user_process_roles
  FOR SELECT USING (
    org_id IN (
      SELECT id FROM organizations WHERE clerk_org_id IN (
        SELECT DISTINCT jsonb_array_elements_text(
          (auth.jwt() -> 'org_memberships')::jsonb
        )
      )
    )
  );

-- Create indexes for better performance
CREATE INDEX idx_organizations_clerk_org_id ON organizations(clerk_org_id);
CREATE INDEX idx_user_process_roles_user_id ON user_process_roles(user_id);
CREATE INDEX idx_user_process_roles_org_id ON user_process_roles(org_id);

-- Function to handle updated_at timestamps
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_organizations_updated_at
  BEFORE UPDATE ON organizations
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

CREATE TRIGGER update_user_process_roles_updated_at
  BEFORE UPDATE ON user_process_roles
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();
