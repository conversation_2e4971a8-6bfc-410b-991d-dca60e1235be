import { pgTable, uuid, text, timestamp, jsonb, boolean } from 'drizzle-orm/pg-core';

// Organizations table (maps to Clerk organizations)
export const organizations = pgTable('organizations', {
  id: uuid('id').primaryKey().defaultRandom(),
  clerkOrgId: text('clerk_org_id').unique().notNull(),
  name: text('name').notNull(),
  slug: text('slug').unique().notNull(),
  type: text('type', { enum: ['recruitment', 'bench_sales', 'hybrid'] }).notNull().default('hybrid'),
  settings: jsonb('settings').default({}),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// User process roles (defines which processes users can access)
export const userProcessRoles = pgTable('user_process_roles', {
  id: uuid('id').primary<PERSON>ey().defaultRandom(),
  userId: text('user_id').notNull(), // Clerk user ID
  orgId: uuid('org_id').references(() => organizations.id).notNull(),
  processType: text('process_type', { enum: ['recruitment', 'bench_sales'] }).notNull(),
  role: text('role', { enum: ['admin', 'manager', 'recruiter', 'coordinator'] }).notNull(),
  permissions: jsonb('permissions').default({}),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});
