import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';

// Create connection pool with full NileDB connection string
const connectionString = `postgres://${process.env.NILEDB_USER}:${process.env.NILEDB_PASSWORD}@us-west-2.db.thenile.dev:5432/TalentHUB?sslmode=require`;

const pool = new Pool({
  connectionString,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Initialize Drizzle
export const db = drizzle(pool);

// Helper function to set tenant context for queries
export const setTenantContext = async (tenantId: string) => {
  await pool.query(`SET nile.tenant_id = '${tenantId}'`);
};
