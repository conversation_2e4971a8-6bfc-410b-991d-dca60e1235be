// Test database connection
import { db } from './db';

export async function testDatabaseConnection() {
  try {
    // Simple query to test connection
    const result = await db.execute('SELECT 1 as test');
    console.log('✅ Database connection successful:', result);
    return { success: true, result };
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return { success: false, error: error.message };
  }
}

export async function createTablesIfNotExist() {
  try {
    // Create organizations table
    await db.execute(`
      CREATE TABLE IF NOT EXISTS organizations (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        clerk_org_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        type TEXT NOT NULL DEFAULT 'hybrid' CHECK (type IN ('recruitment', 'bench_sales', 'hybrid')),
        settings JSONB DEFAULT '{}',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // Create user_process_roles table
    await db.execute(`
      CREATE TABLE IF NOT EXISTS user_process_roles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id TEXT NOT NULL,
        org_id UUID REFERENCES organizations(id) NOT NULL,
        process_type TEXT NOT NULL CHECK (process_type IN ('recruitment', 'bench_sales')),
        role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'recruiter', 'coordinator')),
        permissions JSONB DEFAULT '{}',
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log('✅ Tables created successfully');
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to create tables:', error);
    return { success: false, error: error.message };
  }
}
