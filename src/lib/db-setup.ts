import { supabaseAdmin } from './supabase';

export async function setupDatabase() {
  try {
    console.log('🚀 Setting up TalentHUB database schema...');

    // Read and execute the schema file
    const fs = require('fs');
    const path = require('path');
    const schemaPath = path.join(process.cwd(), 'database-schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');

    // Split by semicolons and execute each statement
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    for (const statement of statements) {
      const { error } = await supabaseAdmin.rpc('exec_sql', { 
        sql: statement 
      });
      
      if (error) {
        console.error('Error executing statement:', statement);
        console.error('Error:', error);
      }
    }

    console.log('✅ Database schema created successfully!');
    return { success: true };

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    return { success: false, error: error.message };
  }
}

export async function testConnection() {
  try {
    // Simple query to test connection
    const { data, error } = await supabaseAdmin
      .from('organizations')
      .select('*')
      .limit(1);

    if (error && error.code !== 'PGRST116') { // PGRST116 means table doesn't exist yet, which is OK
      console.error('Connection test failed:', error);
      return { success: false, error };
    }

    console.log('✅ Supabase connection successful!');
    return { success: true, data };

  } catch (error) {
    console.error('❌ Connection test failed:', error);
    return { success: false, error: error.message };
  }
}
