import { NextRequest, NextResponse } from 'next/server';
import { testConnection, setupDatabase } from '@/lib/db-setup';

export async function GET(request: NextRequest) {
  try {
    // Test Supabase connection
    const connectionTest = await testConnection();
    
    if (!connectionTest.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Supabase connection failed',
          details: connectionTest.error 
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful!',
      database: 'Supabase',
      status: 'Connected'
    });

  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Database test failed',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Set up database schema
    const setupResult = await setupDatabase();
    
    return NextResponse.json({
      success: setupResult.success,
      message: setupResult.success 
        ? 'Database schema created successfully!' 
        : 'Database setup failed',
      error: setupResult.error || null
    });

  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'Database setup failed',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
